# 兜底响应机制插件测试说明

## 插件功能
这个插件实现了一个兜底响应机制，当用户输入的命令没有被任何插件处理时，会提供友好的提示信息。

## 主要特性

### 1. 优先级设置
- 优先级设置为 `Infinity`，确保在所有其他插件之后执行
- 只有当没有其他插件处理命令时才会触发

### 2. 适配器限制
- 只允许 QQBot 适配器使用
- 其他适配器的消息不会触发兜底响应

### 3. 白名单机制
插件包含了完整的白名单，以下命令不会触发兜底响应：

#### plugins/example 插件命令：
- `ww*` - 所有以ww开头的命令
- `#伤害总排行列表` - 伤害排行功能
- `#伤害总排行列表4` - 伤害排行4功能
- `#鸣潮共鸣链列表` - 共鸣链功能
- `#鸣潮图鉴列表` - 图鉴功能
- `#鸣潮天赋列表` - 天赋功能
- `#希腊奶帮助` - 希腊奶功能
- `ww登录` - 登录提示
- `#今日doro结局` 等 - doro结局功能
- `#深塔信息` - 深塔信息
- `#评分总排行列表` - 评分排行功能
- `随机表情包列表` - 表情包功能
- `#ww帮助` 或 `鸣潮帮助` - 帮助功能
- `#帮助` 或 `菜单` - 总菜单功能

#### 系统命令：
- `#状态` / `#统计` - 状态查询
- `#设置主人` - 主人设置
- `#添加` / `#删除` - 消息管理
- `#撤回` - 消息撤回
- `#重启` / `#关机` / `#停机` - 系统控制
- `#版本` - 版本信息
- `#安装` - 插件安装
- `#更新` / `#日志` - 更新相关

### 4. ws-plugin 检测
- 自动检测 ws-plugin 是否安装
- 识别 ws-plugin 相关命令模式
- 动态检查 ws-plugin 的命令规则

### 5. 智能建议
根据用户输入的命令内容，提供相关的建议：
- 包含"帮助"时建议使用 `#帮助` 或 `菜单`
- 包含"ww"或"鸣潮"时建议使用 `ww帮助`
- 包含"伤害"时建议使用伤害排行功能
- 包含"评分"时建议使用评分排行功能
- 等等...

### 6. 消息格式
- 使用 Markdown 模板显示友好的错误信息
- 包含按钮快速访问功能菜单
- 如果 Markdown 发送失败，自动回退到普通文本

## 测试用例

### 应该触发兜底响应的命令：
- `#未知命令`
- `#不存在的功能`
- `#测试123`

### 不应该触发兜底响应的命令：
- `#帮助` (系统命令)
- `ww登录` (example插件命令)
- `#伤害总排行列表` (example插件命令)
- `#状态` (系统命令)
- 非命令消息（不以#开头）

## 安装说明
1. 将 `fallback.js` 文件放置在 `plugins/system/` 目录下
2. 重启 Yunzai 或热重载插件
3. 插件将自动加载并开始工作

## 注意事项
- 插件会在其他所有插件执行完毕后才执行
- 只处理以 `#` 开头的消息（命令）
- 包含消息去重机制，避免重复触发
- 自动清理过期的消息记录，防止内存泄漏
