# 3.1.3

* 支持协议端：QQBot、go-cqhttp → OneBotv11、Lagrange
* **请注意：**
  * 从3.1.3版本开始，原genshin包内的功能会逐步重构，与miao-plugin进行整合，以降低后续游戏版本升级时的维护成本
  * 在整合过程中，可能会移除一些重复或迁移成本较高的功能，以及可能会有功能不稳定情况
  * 如介意，请切换至**v312-backup**分支

# 3.1.2

* 支持协议端：OPQBot
* 新增`#绑定用户`命令
  * 可将其他QQ绑定至当前用户，以打通多个用户，子用户使用主用户的CK与UID等信息
  * 同时也可绑定其他平台的用户，例如频道、微信等。如需链接至其他平台需使用TRSS-Yunzai或Lain-plugin
  * 部分命令可能无法识别绑定后的主用户，如遇问题可反馈

# 3.1.1

* 支持协议端：米游社大别野Bot
* 初步适配原神4.0版本，增加对应资源及信息展示，感谢**Ca(HCO₃)₂**、**@touchscale**、**@teriri7**
* 升级`#探索`内容，支持更多内容展示 **@bangbanbab**
* 增加 `#全部抽卡记录` **@story-x**

# 3.1.0

* 支持协议端：GSUIDCore、微信
* 重构CK与UID管理逻辑
    * 支持多UID绑定，可绑定多个UID并进行切换
    * 支持原神与星铁UID共存，可针对查询命令分配对应UID
    * 新增`#删除uid1`命令，可对`#uid`列表内的绑定UID进行删除
    * 使用sqlite进行ck与uid存储
* 底层对星铁查询进行支持 **@cvs**

# 3.0.2

* 支持协议端：ComWeChat、ICQQ、QQ频道、KOOK、Telegram、Discord
* 3.6卡池以及图像武器别名等数据更新 **@cvs**
* 将渲染逻辑独立，支持扩展渲染器 **@ikuaki**

# 3.0.1

* 支持多账号，支持协议端：go-cqhttp

# 3.0.0

* 从 Miao-Yunzai 分支

# 3.0.0