2025-08-02 01:16:19 [error    ] [SV] json 执行时出现错误!
2025-08-02 01:16:19 [error    ] 'gbk' codec can't decode byte 0xb3 in position 3: illegal multibyte sequence
╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:305 in import_gachalogs                                                         │
│                                                                                                  │
│   302 │   else:                                                                                  │
│   303 │   │   data_bytes = base64.b64decode(history_url)                                         │
│   304 │   │   try:                                                                               │
│ ❱ 305 │   │   │   history_data = json.loads(data_bytes.decode())                                 │
│   306 │   │   except UnicodeDecodeError:                                                         │
│   307 │   │   │   history_data = json.loads(data_bytes.decode("gbk"))                            │
│   308 │   │   except json.decoder.JSONDecodeError:                                               │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\x… │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlk… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadf… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='ada1f5b9-62d1-4821-b0cb-8da06d413d13',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&ist… │ │
│ │                │   file_type='url',                                                          │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │ history_data = {}                                                                            │ │
│ │  history_url = 'https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=… │ │
│ │         type = 'url'                                                                         │ │
│ │          uid = '100381247'                                                                   │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
UnicodeDecodeError: 'utf-8' codec can't decode byte 0x86 in position 0: invalid start byte

During handling of the above exception, another exception occurred:

╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/sv.py:37 in wrapper                                                  │
│                                                                                                  │
│    34 │   @wraps(func)                                                                           │
│    35 │   async def wrapper(bot: Bot, event: Event):                                             │
│    36 │   │   try:                                                                               │
│ ❱  37 │   │   │   result = await func(bot, event)                                                │
│    38 │   │   │   return result                                                                  │
│    39 │   │   except Exception as e:                                                             │
│    40 │   │   │   logger.error(f'[SV] {event.command} 执行时出现错误!')                          │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   bot = <gsuid_core.bot.Bot object at 0x7f7ca01d7d60>                                        │ │
│ │     e = UnicodeDecodeError('gbk',                                                            │ │
│ │         b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\xaa\xa7h… │ │
│ │         3, 4, 'illegal multibyte sequence')                                                  │ │
│ │ event = Event(                                                                               │ │
│ │         │   bot_id='qqgroup',                                                                │ │
│ │         │   bot_self_id='3889000318',                                                        │ │
│ │         │                                                                                    │ │
│ │         msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlknD8fQAM… │ │
│ │         │   user_type='direct',                                                              │ │
│ │         │   group_id=None,                                                                   │ │
│ │         │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                           │ │
│ │         │   sender={                                                                         │ │
│ │         │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │         │   │   'avatar':                                                                    │ │
│ │         'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'              │ │
│ │         │   },                                                                               │ │
│ │         │   user_pm=0,                                                                       │ │
│ │         │   content=[                                                                        │ │
│ │         │   │   Message(                                                                     │ │
│ │         │   │   │   type='file',                                                             │ │
│ │         │   │   │                                                                            │ │
│ │         data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fi… │ │
│ │         │   │   )                                                                            │ │
│ │         │   ],                                                                               │ │
│ │         │   task_id='ada1f5b9-62d1-4821-b0cb-8da06d413d13',                                  │ │
│ │         │   task_event=None,                                                                 │ │
│ │         │   real_bot_id='qqgroup',                                                           │ │
│ │         │   raw_text='',                                                                     │ │
│ │         │   command='json',                                                                  │ │
│ │         │   text='',                                                                         │ │
│ │         │   image=None,                                                                      │ │
│ │         │   at=None,                                                                         │ │
│ │         │   image_list=[],                                                                   │ │
│ │         │   at_list=[],                                                                      │ │
│ │         │   is_tome=False,                                                                   │ │
│ │         │   reply=None,                                                                      │ │
│ │         │   file_name='export_100381247.json',                                               │ │
│ │         │                                                                                    │ │
│ │         file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=0&… │ │
│ │         │   file_type='url',                                                                 │ │
│ │         │   regex_group=(),                                                                  │ │
│ │         │   regex_dict={}                                                                    │ │
│ │         )                                                                                    │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ __init__.py:102 in get_gacha_log_by_file                                                         │
│                                                                                                  │
│    99 │                                                                                          │
│   100 │   if ev.file and ev.file_type:                                                           │
│   101 │   │   await bot.send("正在尝试导入抽卡记录中，请耐心等待……")                             │
│ ❱ 102 │   │   return await bot.send(await import_gachalogs(ev, ev.file, ev.file_type, uid))      │
│   103 │   else:                                                                                  │
│   104 │   │   return await bot.send("导入抽卡记录异常...")                                       │
│   105                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │ bot = <gsuid_core.bot.Bot object at 0x7f7ca01d7d60>                                          │ │
│ │  ev = Event(                                                                                 │ │
│ │       │   bot_id='qqgroup',                                                                  │ │
│ │       │   bot_self_id='3889000318',                                                          │ │
│ │       │                                                                                      │ │
│ │       msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlknD8fQAMFT… │ │
│ │       │   user_type='direct',                                                                │ │
│ │       │   group_id=None,                                                                     │ │
│ │       │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                             │ │
│ │       │   sender={                                                                           │ │
│ │       │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                      │ │
│ │       │   │   'avatar':                                                                      │ │
│ │       'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'                │ │
│ │       │   },                                                                                 │ │
│ │       │   user_pm=0,                                                                         │ │
│ │       │   content=[                                                                          │ │
│ │       │   │   Message(                                                                       │ │
│ │       │   │   │   type='file',                                                               │ │
│ │       │   │   │                                                                              │ │
│ │       data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?file… │ │
│ │       │   │   )                                                                              │ │
│ │       │   ],                                                                                 │ │
│ │       │   task_id='ada1f5b9-62d1-4821-b0cb-8da06d413d13',                                    │ │
│ │       │   task_event=None,                                                                   │ │
│ │       │   real_bot_id='qqgroup',                                                             │ │
│ │       │   raw_text='',                                                                       │ │
│ │       │   command='json',                                                                    │ │
│ │       │   text='',                                                                           │ │
│ │       │   image=None,                                                                        │ │
│ │       │   at=None,                                                                           │ │
│ │       │   image_list=[],                                                                     │ │
│ │       │   at_list=[],                                                                        │ │
│ │       │   is_tome=False,                                                                     │ │
│ │       │   reply=None,                                                                        │ │
│ │       │   file_name='export_100381247.json',                                                 │ │
│ │       │                                                                                      │ │
│ │       file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=0&op… │ │
│ │       │   file_type='url',                                                                   │ │
│ │       │   regex_group=(),                                                                    │ │
│ │       │   regex_dict={}                                                                      │ │
│ │       )                                                                                      │ │
│ │ uid = '100381247'                                                                            │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:307 in import_gachalogs                                                         │
│                                                                                                  │
│   304 │   │   try:                                                                               │
│   305 │   │   │   history_data = json.loads(data_bytes.decode())                                 │
│   306 │   │   except UnicodeDecodeError:                                                         │
│ ❱ 307 │   │   │   history_data = json.loads(data_bytes.decode("gbk"))                            │
│   308 │   │   except json.decoder.JSONDecodeError:                                               │
│   309 │   │   │   return "请传入正确的JSON格式文件!"                                             │
│   310                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\x… │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlk… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadf… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='ada1f5b9-62d1-4821-b0cb-8da06d413d13',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&ist… │ │
│ │                │   file_type='url',                                                          │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │ history_data = {}                                                                            │ │
│ │  history_url = 'https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=… │ │
│ │         type = 'url'                                                                         │ │
│ │          uid = '100381247'                                                                   │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
UnicodeDecodeError: 'gbk' codec can't decode byte 0xb3 in position 3: illegal multibyte sequence

2025-08-02 01:16:19 [info     ] [发送消息to] qqgroup - direct - 3889000318:4B8A766C5CA11917527B3FCD14830714
2025-08-02 01:16:19 [error    ] [SV] json 执行时出现错误!
2025-08-02 01:16:19 [error    ] 'gbk' codec can't decode byte 0xb3 in position 3: illegal multibyte sequence
╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:305 in import_gachalogs                                                         │
│                                                                                                  │
│   302 │   else:                                                                                  │
│   303 │   │   data_bytes = base64.b64decode(history_url)                                         │
│   304 │   │   try:                                                                               │
│ ❱ 305 │   │   │   history_data = json.loads(data_bytes.decode())                                 │
│   306 │   │   except UnicodeDecodeError:                                                         │
│   307 │   │   │   history_data = json.loads(data_bytes.decode("gbk"))                            │
│   308 │   │   except json.decoder.JSONDecodeError:                                               │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\x… │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlk… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadf… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='d2861db4-1449-4ecc-8963-1c02889e9f4a',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&ist… │ │
│ │                │   file_type='url',                                                          │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │ history_data = {}                                                                            │ │
│ │  history_url = 'https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=… │ │
│ │         type = 'url'                                                                         │ │
│ │          uid = '100381247'                                                                   │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
UnicodeDecodeError: 'utf-8' codec can't decode byte 0x86 in position 0: invalid start byte

During handling of the above exception, another exception occurred:

╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/sv.py:37 in wrapper                                                  │
│                                                                                                  │
│    34 │   @wraps(func)                                                                           │
│    35 │   async def wrapper(bot: Bot, event: Event):                                             │
│    36 │   │   try:                                                                               │
│ ❱  37 │   │   │   result = await func(bot, event)                                                │
│    38 │   │   │   return result                                                                  │
│    39 │   │   except Exception as e:                                                             │
│    40 │   │   │   logger.error(f'[SV] {event.command} 执行时出现错误!')                          │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   bot = <gsuid_core.bot.Bot object at 0x7f7ca01d6200>                                        │ │
│ │     e = UnicodeDecodeError('gbk',                                                            │ │
│ │         b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\xaa\xa7h… │ │
│ │         3, 4, 'illegal multibyte sequence')                                                  │ │
│ │ event = Event(                                                                               │ │
│ │         │   bot_id='qqgroup',                                                                │ │
│ │         │   bot_self_id='3889000318',                                                        │ │
│ │         │                                                                                    │ │
│ │         msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlknD8fQAM… │ │
│ │         │   user_type='direct',                                                              │ │
│ │         │   group_id=None,                                                                   │ │
│ │         │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                           │ │
│ │         │   sender={                                                                         │ │
│ │         │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │         │   │   'avatar':                                                                    │ │
│ │         'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'              │ │
│ │         │   },                                                                               │ │
│ │         │   user_pm=0,                                                                       │ │
│ │         │   content=[                                                                        │ │
│ │         │   │   Message(                                                                     │ │
│ │         │   │   │   type='file',                                                             │ │
│ │         │   │   │                                                                            │ │
│ │         data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fi… │ │
│ │         │   │   )                                                                            │ │
│ │         │   ],                                                                               │ │
│ │         │   task_id='d2861db4-1449-4ecc-8963-1c02889e9f4a',                                  │ │
│ │         │   task_event=None,                                                                 │ │
│ │         │   real_bot_id='qqgroup',                                                           │ │
│ │         │   raw_text='',                                                                     │ │
│ │         │   command='json',                                                                  │ │
│ │         │   text='',                                                                         │ │
│ │         │   image=None,                                                                      │ │
│ │         │   at=None,                                                                         │ │
│ │         │   image_list=[],                                                                   │ │
│ │         │   at_list=[],                                                                      │ │
│ │         │   is_tome=False,                                                                   │ │
│ │         │   reply=None,                                                                      │ │
│ │         │   file_name='export_100381247.json',                                               │ │
│ │         │                                                                                    │ │
│ │         file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=0&… │ │
│ │         │   file_type='url',                                                                 │ │
│ │         │   regex_group=(),                                                                  │ │
│ │         │   regex_dict={}                                                                    │ │
│ │         )                                                                                    │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ __init__.py:102 in get_gacha_log_by_file                                                         │
│                                                                                                  │
│    99 │                                                                                          │
│   100 │   if ev.file and ev.file_type:                                                           │
│   101 │   │   await bot.send("正在尝试导入抽卡记录中，请耐心等待……")                             │
│ ❱ 102 │   │   return await bot.send(await import_gachalogs(ev, ev.file, ev.file_type, uid))      │
│   103 │   else:                                                                                  │
│   104 │   │   return await bot.send("导入抽卡记录异常...")                                       │
│   105                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │ bot = <gsuid_core.bot.Bot object at 0x7f7ca01d6200>                                          │ │
│ │  ev = Event(                                                                                 │ │
│ │       │   bot_id='qqgroup',                                                                  │ │
│ │       │   bot_self_id='3889000318',                                                          │ │
│ │       │                                                                                      │ │
│ │       msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlknD8fQAMFT… │ │
│ │       │   user_type='direct',                                                                │ │
│ │       │   group_id=None,                                                                     │ │
│ │       │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                             │ │
│ │       │   sender={                                                                           │ │
│ │       │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                      │ │
│ │       │   │   'avatar':                                                                      │ │
│ │       'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'                │ │
│ │       │   },                                                                                 │ │
│ │       │   user_pm=0,                                                                         │ │
│ │       │   content=[                                                                          │ │
│ │       │   │   Message(                                                                       │ │
│ │       │   │   │   type='file',                                                               │ │
│ │       │   │   │                                                                              │ │
│ │       data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?file… │ │
│ │       │   │   )                                                                              │ │
│ │       │   ],                                                                                 │ │
│ │       │   task_id='d2861db4-1449-4ecc-8963-1c02889e9f4a',                                    │ │
│ │       │   task_event=None,                                                                   │ │
│ │       │   real_bot_id='qqgroup',                                                             │ │
│ │       │   raw_text='',                                                                       │ │
│ │       │   command='json',                                                                    │ │
│ │       │   text='',                                                                           │ │
│ │       │   image=None,                                                                        │ │
│ │       │   at=None,                                                                           │ │
│ │       │   image_list=[],                                                                     │ │
│ │       │   at_list=[],                                                                        │ │
│ │       │   is_tome=False,                                                                     │ │
│ │       │   reply=None,                                                                        │ │
│ │       │   file_name='export_100381247.json',                                                 │ │
│ │       │                                                                                      │ │
│ │       file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=0&op… │ │
│ │       │   file_type='url',                                                                   │ │
│ │       │   regex_group=(),                                                                    │ │
│ │       │   regex_dict={}                                                                      │ │
│ │       )                                                                                      │ │
│ │ uid = '100381247'                                                                            │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:307 in import_gachalogs                                                         │
│                                                                                                  │
│   304 │   │   try:                                                                               │
│   305 │   │   │   history_data = json.loads(data_bytes.decode())                                 │
│   306 │   │   except UnicodeDecodeError:                                                         │
│ ❱ 307 │   │   │   history_data = json.loads(data_bytes.decode("gbk"))                            │
│   308 │   │   except json.decoder.JSONDecodeError:                                               │
│   309 │   │   │   return "请传入正确的JSON格式文件!"                                             │
│   310                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\x… │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlk… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadf… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='d2861db4-1449-4ecc-8963-1c02889e9f4a',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&ist… │ │
│ │                │   file_type='url',                                                          │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │ history_data = {}                                                                            │ │
│ │  history_url = 'https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=… │ │
│ │         type = 'url'                                                                         │ │
│ │          uid = '100381247'                                                                   │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
UnicodeDecodeError: 'gbk' codec can't decode byte 0xb3 in position 3: illegal multibyte sequence

2025-08-02 01:16:19 [info     ] [发送消息to] qqgroup - direct - 3889000318:4B8A766C5CA11917527B3FCD14830714
2025-08-02 01:16:19 [error    ] [SV] json 执行时出现错误!
2025-08-02 01:16:19 [error    ] 'gbk' codec can't decode byte 0xb3 in position 3: illegal multibyte sequence
╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:305 in import_gachalogs                                                         │
│                                                                                                  │
│   302 │   else:                                                                                  │
│   303 │   │   data_bytes = base64.b64decode(history_url)                                         │
│   304 │   │   try:                                                                               │
│ ❱ 305 │   │   │   history_data = json.loads(data_bytes.decode())                                 │
│   306 │   │   except UnicodeDecodeError:                                                         │
│   307 │   │   │   history_data = json.loads(data_bytes.decode("gbk"))                            │
│   308 │   │   except json.decoder.JSONDecodeError:                                               │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\x… │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlk… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadf… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='d3aeb254-503c-477e-b719-46938a0209df',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&ist… │ │
│ │                │   file_type='url',                                                          │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │ history_data = {}                                                                            │ │
│ │  history_url = 'https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=… │ │
│ │         type = 'url'                                                                         │ │
│ │          uid = '100381247'                                                                   │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
UnicodeDecodeError: 'utf-8' codec can't decode byte 0x86 in position 0: invalid start byte

During handling of the above exception, another exception occurred:

╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/sv.py:37 in wrapper                                                  │
│                                                                                                  │
│    34 │   @wraps(func)                                                                           │
│    35 │   async def wrapper(bot: Bot, event: Event):                                             │
│    36 │   │   try:                                                                               │
│ ❱  37 │   │   │   result = await func(bot, event)                                                │
│    38 │   │   │   return result                                                                  │
│    39 │   │   except Exception as e:                                                             │
│    40 │   │   │   logger.error(f'[SV] {event.command} 执行时出现错误!')                          │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   bot = <gsuid_core.bot.Bot object at 0x7f7ca01d7160>                                        │ │
│ │     e = UnicodeDecodeError('gbk',                                                            │ │
│ │         b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\xaa\xa7h… │ │
│ │         3, 4, 'illegal multibyte sequence')                                                  │ │
│ │ event = Event(                                                                               │ │
│ │         │   bot_id='qqgroup',                                                                │ │
│ │         │   bot_self_id='3889000318',                                                        │ │
│ │         │                                                                                    │ │
│ │         msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlknD8fQAM… │ │
│ │         │   user_type='direct',                                                              │ │
│ │         │   group_id=None,                                                                   │ │
│ │         │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                           │ │
│ │         │   sender={                                                                         │ │
│ │         │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │         │   │   'avatar':                                                                    │ │
│ │         'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'              │ │
│ │         │   },                                                                               │ │
│ │         │   user_pm=0,                                                                       │ │
│ │         │   content=[                                                                        │ │
│ │         │   │   Message(                                                                     │ │
│ │         │   │   │   type='file',                                                             │ │
│ │         │   │   │                                                                            │ │
│ │         data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fi… │ │
│ │         │   │   )                                                                            │ │
│ │         │   ],                                                                               │ │
│ │         │   task_id='d3aeb254-503c-477e-b719-46938a0209df',                                  │ │
│ │         │   task_event=None,                                                                 │ │
│ │         │   real_bot_id='qqgroup',                                                           │ │
│ │         │   raw_text='',                                                                     │ │
│ │         │   command='json',                                                                  │ │
│ │         │   text='',                                                                         │ │
│ │         │   image=None,                                                                      │ │
│ │         │   at=None,                                                                         │ │
│ │         │   image_list=[],                                                                   │ │
│ │         │   at_list=[],                                                                      │ │
│ │         │   is_tome=False,                                                                   │ │
│ │         │   reply=None,                                                                      │ │
│ │         │   file_name='export_100381247.json',                                               │ │
│ │         │                                                                                    │ │
│ │         file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=0&… │ │
│ │         │   file_type='url',                                                                 │ │
│ │         │   regex_group=(),                                                                  │ │
│ │         │   regex_dict={}                                                                    │ │
│ │         )                                                                                    │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ __init__.py:102 in get_gacha_log_by_file                                                         │
│                                                                                                  │
│    99 │                                                                                          │
│   100 │   if ev.file and ev.file_type:                                                           │
│   101 │   │   await bot.send("正在尝试导入抽卡记录中，请耐心等待……")                             │
│ ❱ 102 │   │   return await bot.send(await import_gachalogs(ev, ev.file, ev.file_type, uid))      │
│   103 │   else:                                                                                  │
│   104 │   │   return await bot.send("导入抽卡记录异常...")                                       │
│   105                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │ bot = <gsuid_core.bot.Bot object at 0x7f7ca01d7160>                                          │ │
│ │  ev = Event(                                                                                 │ │
│ │       │   bot_id='qqgroup',                                                                  │ │
│ │       │   bot_self_id='3889000318',                                                          │ │
│ │       │                                                                                      │ │
│ │       msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlknD8fQAMFT… │ │
│ │       │   user_type='direct',                                                                │ │
│ │       │   group_id=None,                                                                     │ │
│ │       │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                             │ │
│ │       │   sender={                                                                           │ │
│ │       │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                      │ │
│ │       │   │   'avatar':                                                                      │ │
│ │       'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'                │ │
│ │       │   },                                                                                 │ │
│ │       │   user_pm=0,                                                                         │ │
│ │       │   content=[                                                                          │ │
│ │       │   │   Message(                                                                       │ │
│ │       │   │   │   type='file',                                                               │ │
│ │       │   │   │                                                                              │ │
│ │       data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?file… │ │
│ │       │   │   )                                                                              │ │
│ │       │   ],                                                                                 │ │
│ │       │   task_id='d3aeb254-503c-477e-b719-46938a0209df',                                    │ │
│ │       │   task_event=None,                                                                   │ │
│ │       │   real_bot_id='qqgroup',                                                             │ │
│ │       │   raw_text='',                                                                       │ │
│ │       │   command='json',                                                                    │ │
│ │       │   text='',                                                                           │ │
│ │       │   image=None,                                                                        │ │
│ │       │   at=None,                                                                           │ │
│ │       │   image_list=[],                                                                     │ │
│ │       │   at_list=[],                                                                        │ │
│ │       │   is_tome=False,                                                                     │ │
│ │       │   reply=None,                                                                        │ │
│ │       │   file_name='export_100381247.json',                                                 │ │
│ │       │                                                                                      │ │
│ │       file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=0&op… │ │
│ │       │   file_type='url',                                                                   │ │
│ │       │   regex_group=(),                                                                    │ │
│ │       │   regex_dict={}                                                                      │ │
│ │       )                                                                                      │ │
│ │ uid = '100381247'                                                                            │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:307 in import_gachalogs                                                         │
│                                                                                                  │
│   304 │   │   try:                                                                               │
│   305 │   │   │   history_data = json.loads(data_bytes.decode())                                 │
│   306 │   │   except UnicodeDecodeError:                                                         │
│ ❱ 307 │   │   │   history_data = json.loads(data_bytes.decode("gbk"))                            │
│   308 │   │   except json.decoder.JSONDecodeError:                                               │
│   309 │   │   │   return "请传入正确的JSON格式文件!"                                             │
│   310                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\x… │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlk… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadf… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='d3aeb254-503c-477e-b719-46938a0209df',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&ist… │ │
│ │                │   file_type='url',                                                          │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │ history_data = {}                                                                            │ │
│ │  history_url = 'https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=… │ │
│ │         type = 'url'                                                                         │ │
│ │          uid = '100381247'                                                                   │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
UnicodeDecodeError: 'gbk' codec can't decode byte 0xb3 in position 3: illegal multibyte sequence

2025-08-02 01:16:19 [info     ] [发送消息to] qqgroup - direct - 3889000318:4B8A766C5CA11917527B3FCD14830714
2025-08-02 01:16:19 [error    ] [SV] json 执行时出现错误!
2025-08-02 01:16:19 [error    ] 'gbk' codec can't decode byte 0xb3 in position 3: illegal multibyte sequence
╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:305 in import_gachalogs                                                         │
│                                                                                                  │
│   302 │   else:                                                                                  │
│   303 │   │   data_bytes = base64.b64decode(history_url)                                         │
│   304 │   │   try:                                                                               │
│ ❱ 305 │   │   │   history_data = json.loads(data_bytes.decode())                                 │
│   306 │   │   except UnicodeDecodeError:                                                         │
│   307 │   │   │   history_data = json.loads(data_bytes.decode("gbk"))                            │
│   308 │   │   except json.decoder.JSONDecodeError:                                               │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\x… │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlk… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadf… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='b5ddc92a-0723-48d6-9cd4-cbad16104d54',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&ist… │ │
│ │                │   file_type='url',                                                          │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │ history_data = {}                                                                            │ │
│ │  history_url = 'https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=… │ │
│ │         type = 'url'                                                                         │ │
│ │          uid = '100381247'                                                                   │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
UnicodeDecodeError: 'utf-8' codec can't decode byte 0x86 in position 0: invalid start byte

During handling of the above exception, another exception occurred:

╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/sv.py:37 in wrapper                                                  │
│                                                                                                  │
│    34 │   @wraps(func)                                                                           │
│    35 │   async def wrapper(bot: Bot, event: Event):                                             │
│    36 │   │   try:                                                                               │
│ ❱  37 │   │   │   result = await func(bot, event)                                                │
│    38 │   │   │   return result                                                                  │
│    39 │   │   except Exception as e:                                                             │
│    40 │   │   │   logger.error(f'[SV] {event.command} 执行时出现错误!')                          │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   bot = <gsuid_core.bot.Bot object at 0x7f7ca01d7040>                                        │ │
│ │     e = UnicodeDecodeError('gbk',                                                            │ │
│ │         b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\xaa\xa7h… │ │
│ │         3, 4, 'illegal multibyte sequence')                                                  │ │
│ │ event = Event(                                                                               │ │
│ │         │   bot_id='qqgroup',                                                                │ │
│ │         │   bot_self_id='3889000318',                                                        │ │
│ │         │                                                                                    │ │
│ │         msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlknD8fQAM… │ │
│ │         │   user_type='direct',                                                              │ │
│ │         │   group_id=None,                                                                   │ │
│ │         │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                           │ │
│ │         │   sender={                                                                         │ │
│ │         │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │         │   │   'avatar':                                                                    │ │
│ │         'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'              │ │
│ │         │   },                                                                               │ │
│ │         │   user_pm=0,                                                                       │ │
│ │         │   content=[                                                                        │ │
│ │         │   │   Message(                                                                     │ │
│ │         │   │   │   type='file',                                                             │ │
│ │         │   │   │                                                                            │ │
│ │         data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fi… │ │
│ │         │   │   )                                                                            │ │
│ │         │   ],                                                                               │ │
│ │         │   task_id='b5ddc92a-0723-48d6-9cd4-cbad16104d54',                                  │ │
│ │         │   task_event=None,                                                                 │ │
│ │         │   real_bot_id='qqgroup',                                                           │ │
│ │         │   raw_text='',                                                                     │ │
│ │         │   command='json',                                                                  │ │
│ │         │   text='',                                                                         │ │
│ │         │   image=None,                                                                      │ │
│ │         │   at=None,                                                                         │ │
│ │         │   image_list=[],                                                                   │ │
│ │         │   at_list=[],                                                                      │ │
│ │         │   is_tome=False,                                                                   │ │
│ │         │   reply=None,                                                                      │ │
│ │         │   file_name='export_100381247.json',                                               │ │
│ │         │                                                                                    │ │
│ │         file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=0&… │ │
│ │         │   file_type='url',                                                                 │ │
│ │         │   regex_group=(),                                                                  │ │
│ │         │   regex_dict={}                                                                    │ │
│ │         )                                                                                    │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ __init__.py:102 in get_gacha_log_by_file                                                         │
│                                                                                                  │
│    99 │                                                                                          │
│   100 │   if ev.file and ev.file_type:                                                           │
│   101 │   │   await bot.send("正在尝试导入抽卡记录中，请耐心等待……")                             │
│ ❱ 102 │   │   return await bot.send(await import_gachalogs(ev, ev.file, ev.file_type, uid))      │
│   103 │   else:                                                                                  │
│   104 │   │   return await bot.send("导入抽卡记录异常...")                                       │
│   105                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │ bot = <gsuid_core.bot.Bot object at 0x7f7ca01d7040>                                          │ │
│ │  ev = Event(                                                                                 │ │
│ │       │   bot_id='qqgroup',                                                                  │ │
│ │       │   bot_self_id='3889000318',                                                          │ │
│ │       │                                                                                      │ │
│ │       msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlknD8fQAMFT… │ │
│ │       │   user_type='direct',                                                                │ │
│ │       │   group_id=None,                                                                     │ │
│ │       │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                             │ │
│ │       │   sender={                                                                           │ │
│ │       │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                      │ │
│ │       │   │   'avatar':                                                                      │ │
│ │       'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'                │ │
│ │       │   },                                                                                 │ │
│ │       │   user_pm=0,                                                                         │ │
│ │       │   content=[                                                                          │ │
│ │       │   │   Message(                                                                       │ │
│ │       │   │   │   type='file',                                                               │ │
│ │       │   │   │                                                                              │ │
│ │       data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?file… │ │
│ │       │   │   )                                                                              │ │
│ │       │   ],                                                                                 │ │
│ │       │   task_id='b5ddc92a-0723-48d6-9cd4-cbad16104d54',                                    │ │
│ │       │   task_event=None,                                                                   │ │
│ │       │   real_bot_id='qqgroup',                                                             │ │
│ │       │   raw_text='',                                                                       │ │
│ │       │   command='json',                                                                    │ │
│ │       │   text='',                                                                           │ │
│ │       │   image=None,                                                                        │ │
│ │       │   at=None,                                                                           │ │
│ │       │   image_list=[],                                                                     │ │
│ │       │   at_list=[],                                                                        │ │
│ │       │   is_tome=False,                                                                     │ │
│ │       │   reply=None,                                                                        │ │
│ │       │   file_name='export_100381247.json',                                                 │ │
│ │       │                                                                                      │ │
│ │       file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=0&op… │ │
│ │       │   file_type='url',                                                                   │ │
│ │       │   regex_group=(),                                                                    │ │
│ │       │   regex_dict={}                                                                      │ │
│ │       )                                                                                      │ │
│ │ uid = '100381247'                                                                            │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:307 in import_gachalogs                                                         │
│                                                                                                  │
│   304 │   │   try:                                                                               │
│   305 │   │   │   history_data = json.loads(data_bytes.decode())                                 │
│   306 │   │   except UnicodeDecodeError:                                                         │
│ ❱ 307 │   │   │   history_data = json.loads(data_bytes.decode("gbk"))                            │
│   308 │   │   except json.decoder.JSONDecodeError:                                               │
│   309 │   │   │   return "请传入正确的JSON格式文件!"                                             │
│   310                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\x… │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlk… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadf… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='b5ddc92a-0723-48d6-9cd4-cbad16104d54',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&ist… │ │
│ │                │   file_type='url',                                                          │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │ history_data = {}                                                                            │ │
│ │  history_url = 'https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=… │ │
│ │         type = 'url'                                                                         │ │
│ │          uid = '100381247'                                                                   │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
UnicodeDecodeError: 'gbk' codec can't decode byte 0xb3 in position 3: illegal multibyte sequence

2025-08-02 01:16:19 [info     ] [发送消息to] qqgroup - direct - 3889000318:4B8A766C5CA11917527B3FCD14830714
2025-08-02 01:16:19 [error    ] [SV] json 执行时出现错误!
2025-08-02 01:16:19 [error    ] 'gbk' codec can't decode byte 0xb3 in position 3: illegal multibyte sequence
╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:305 in import_gachalogs                                                         │
│                                                                                                  │
│   302 │   else:                                                                                  │
│   303 │   │   data_bytes = base64.b64decode(history_url)                                         │
│   304 │   │   try:                                                                               │
│ ❱ 305 │   │   │   history_data = json.loads(data_bytes.decode())                                 │
│   306 │   │   except UnicodeDecodeError:                                                         │
│   307 │   │   │   history_data = json.loads(data_bytes.decode("gbk"))                            │
│   308 │   │   except json.decoder.JSONDecodeError:                                               │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\x… │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlk… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadf… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='5fe3d740-6119-496b-9a71-d7c3166c4091',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&ist… │ │
│ │                │   file_type='url',                                                          │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │ history_data = {}                                                                            │ │
│ │  history_url = 'https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=… │ │
│ │         type = 'url'                                                                         │ │
│ │          uid = '100381247'                                                                   │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
UnicodeDecodeError: 'utf-8' codec can't decode byte 0x86 in position 0: invalid start byte

During handling of the above exception, another exception occurred:

╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/sv.py:37 in wrapper                                                  │
│                                                                                                  │
│    34 │   @wraps(func)                                                                           │
│    35 │   async def wrapper(bot: Bot, event: Event):                                             │
│    36 │   │   try:                                                                               │
│ ❱  37 │   │   │   result = await func(bot, event)                                                │
│    38 │   │   │   return result                                                                  │
│    39 │   │   except Exception as e:                                                             │
│    40 │   │   │   logger.error(f'[SV] {event.command} 执行时出现错误!')                          │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   bot = <gsuid_core.bot.Bot object at 0x7f7ca01d4580>                                        │ │
│ │     e = UnicodeDecodeError('gbk',                                                            │ │
│ │         b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\xaa\xa7h… │ │
│ │         3, 4, 'illegal multibyte sequence')                                                  │ │
│ │ event = Event(                                                                               │ │
│ │         │   bot_id='qqgroup',                                                                │ │
│ │         │   bot_self_id='3889000318',                                                        │ │
│ │         │                                                                                    │ │
│ │         msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlknD8fQAM… │ │
│ │         │   user_type='direct',                                                              │ │
│ │         │   group_id=None,                                                                   │ │
│ │         │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                           │ │
│ │         │   sender={                                                                         │ │
│ │         │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │         │   │   'avatar':                                                                    │ │
│ │         'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'              │ │
│ │         │   },                                                                               │ │
│ │         │   user_pm=0,                                                                       │ │
│ │         │   content=[                                                                        │ │
│ │         │   │   Message(                                                                     │ │
│ │         │   │   │   type='file',                                                             │ │
│ │         │   │   │                                                                            │ │
│ │         data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fi… │ │
│ │         │   │   )                                                                            │ │
│ │         │   ],                                                                               │ │
│ │         │   task_id='5fe3d740-6119-496b-9a71-d7c3166c4091',                                  │ │
│ │         │   task_event=None,                                                                 │ │
│ │         │   real_bot_id='qqgroup',                                                           │ │
│ │         │   raw_text='',                                                                     │ │
│ │         │   command='json',                                                                  │ │
│ │         │   text='',                                                                         │ │
│ │         │   image=None,                                                                      │ │
│ │         │   at=None,                                                                         │ │
│ │         │   image_list=[],                                                                   │ │
│ │         │   at_list=[],                                                                      │ │
│ │         │   is_tome=False,                                                                   │ │
│ │         │   reply=None,                                                                      │ │
│ │         │   file_name='export_100381247.json',                                               │ │
│ │         │                                                                                    │ │
│ │         file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=0&… │ │
│ │         │   file_type='url',                                                                 │ │
│ │         │   regex_group=(),                                                                  │ │
│ │         │   regex_dict={}                                                                    │ │
│ │         )                                                                                    │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ __init__.py:102 in get_gacha_log_by_file                                                         │
│                                                                                                  │
│    99 │                                                                                          │
│   100 │   if ev.file and ev.file_type:                                                           │
│   101 │   │   await bot.send("正在尝试导入抽卡记录中，请耐心等待……")                             │
│ ❱ 102 │   │   return await bot.send(await import_gachalogs(ev, ev.file, ev.file_type, uid))      │
│   103 │   else:                                                                                  │
│   104 │   │   return await bot.send("导入抽卡记录异常...")                                       │
│   105                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │ bot = <gsuid_core.bot.Bot object at 0x7f7ca01d4580>                                          │ │
│ │  ev = Event(                                                                                 │ │
│ │       │   bot_id='qqgroup',                                                                  │ │
│ │       │   bot_self_id='3889000318',                                                          │ │
│ │       │                                                                                      │ │
│ │       msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlknD8fQAMFT… │ │
│ │       │   user_type='direct',                                                                │ │
│ │       │   group_id=None,                                                                     │ │
│ │       │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                             │ │
│ │       │   sender={                                                                           │ │
│ │       │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                      │ │
│ │       │   │   'avatar':                                                                      │ │
│ │       'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'                │ │
│ │       │   },                                                                                 │ │
│ │       │   user_pm=0,                                                                         │ │
│ │       │   content=[                                                                          │ │
│ │       │   │   Message(                                                                       │ │
│ │       │   │   │   type='file',                                                               │ │
│ │       │   │   │                                                                              │ │
│ │       data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?file… │ │
│ │       │   │   )                                                                              │ │
│ │       │   ],                                                                                 │ │
│ │       │   task_id='5fe3d740-6119-496b-9a71-d7c3166c4091',                                    │ │
│ │       │   task_event=None,                                                                   │ │
│ │       │   real_bot_id='qqgroup',                                                             │ │
│ │       │   raw_text='',                                                                       │ │
│ │       │   command='json',                                                                    │ │
│ │       │   text='',                                                                           │ │
│ │       │   image=None,                                                                        │ │
│ │       │   at=None,                                                                           │ │
│ │       │   image_list=[],                                                                     │ │
│ │       │   at_list=[],                                                                        │ │
│ │       │   is_tome=False,                                                                     │ │
│ │       │   reply=None,                                                                        │ │
│ │       │   file_name='export_100381247.json',                                                 │ │
│ │       │                                                                                      │ │
│ │       file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=0&op… │ │
│ │       │   file_type='url',                                                                   │ │
│ │       │   regex_group=(),                                                                    │ │
│ │       │   regex_dict={}                                                                      │ │
│ │       )                                                                                      │ │
│ │ uid = '100381247'                                                                            │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:307 in import_gachalogs                                                         │
│                                                                                                  │
│   304 │   │   try:                                                                               │
│   305 │   │   │   history_data = json.loads(data_bytes.decode())                                 │
│   306 │   │   except UnicodeDecodeError:                                                         │
│ ❱ 307 │   │   │   history_data = json.loads(data_bytes.decode("gbk"))                            │
│   308 │   │   except json.decoder.JSONDecodeError:                                               │
│   309 │   │   │   return "请传入正确的JSON格式文件!"                                             │
│   310                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b"\x86\xdbi\xb3\xff\xe0\xae\x8b\xa9\xb5\xa9dsg*\xa9\xca&\xfd\xab'r\x89\xbf\x… │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuDGlkzrewfeDMyTZQYaPQ.QCQE3RLr--o6JvRlk… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|https://grouptalk.c2c.qq.com/asn.com/qqdownloadf… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='5fe3d740-6119-496b-9a71-d7c3166c4091',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&ist… │ │
│ │                │   file_type='url',                                                          │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │ history_data = {}                                                                            │ │
│ │  history_url = 'https://grouptalk.c2c.qq.com/asn.com/qqdownloadftnv5?fileType=4001&isthumb=… │ │
│ │         type = 'url'                                                                         │ │
│ │          uid = '100381247'                                                                   │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
UnicodeDecodeError: 'gbk' codec can't decode byte 0xb3 in position 3: illegal multibyte sequence