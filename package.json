{"name": "trss-yunzai", "version": "3.1.3", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "description": "Bot", "main": "app.js", "type": "module", "scripts": {"app": "node .", "dev": "node . dev", "web": "node lib/tools/web.js", "start": "pm2 start config/pm2.yaml", "stop": "pm2 stop config/pm2.yaml", "restart": "pm2 restart config/pm2.yaml", "log": "pm2 log --lines 100", "lint": "git ls-files '*.js'|xargs prettier --write --list-different"}, "dependencies": {"art-template": "4.13.2", "chalk": "^5.4.1", "chokidar": "^4.0.3", "express": "^4.21.2", "file-type": "^21.0.0", "https-proxy-agent": "7.0.6", "image-size": "^2.0.2", "level": "^10.0.0", "lodash": "^4.17.21", "log4js": "^6.9.1", "md5": "link:lib/modules/md5", "moment": "^2.30.1", "node-fetch": "link:lib/modules/node-fetch", "node-schedule": "^2.1.1", "oicq": "link:lib/modules/oicq", "pm2": "^6.0.8", "puppeteer": "*", "redis": "^4.7.1", "sequelize": "^6.37.7", "sqlite3": "5.1.6", "strip-ansi": "^7.1.0", "ulid": "^3.0.1", "ws": "^8.18.2", "yaml": "^2.8.0"}, "imports": {"#miao": "./plugins/miao-plugin/components/index.js", "#miao.models": "./plugins/miao-plugin/models/index.js"}, "pnpm": {"onlyBuiltDependencies": ["classic-level", "log4js", "puppeteer", "sharp", "sqlite3"], "patchedDependencies": {"log4js@6.9.1": "lib/modules/log4js.patch"}}, "devDependencies": {"prettier": "^3.6.2"}}