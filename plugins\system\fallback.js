export class fallback extends plugin {
  constructor() {
    super({
      name: "兜底响应机制",
      dsc: "当没有插件处理命令时的兜底响应",
      event: "message",
      priority: 10000, // 很低的优先级，但不是无限
      rule: [
        {
          reg: ".*", // 匹配所有消息
          fnc: "fallbackResponse",
          log: false,
        },
      ],
    })

    // plugins/example 插件的白名单命令正则表达式
    this.exampleWhitelist = [
      // 带#的命令
      /^#伤害总排行列表$/, // 伤害排行.js
      /^#伤害总排行列表4$/, // 伤害排行4.js
      /^#鸣潮共鸣链列表$/, // 共鸣链.js
      /^#鸣潮图鉴列表$/, // 图鉴.js
      /^#鸣潮天赋列表$/, // 天赋.js
      /^#希腊奶帮助$/, // 希腊奶.js
      /^#深塔信息$/, // 深塔信息.js
      /^#评分总排行列表$/, // 评分排行.js
      /^#评分总排行列表4$/, // 评分排行4.js
      /^#?(抽取|随机)?(今日|每日)?(Doro|doro)结局$/, // 每日doro结局.js
      /^#?(ww帮助|鸣潮帮助)$/, // 鸣潮帮助.js

      // 不带#的命令
      /^随机表情包列表$/, // 随机表情包按钮.js
      /^(\/帮助|菜单|help|帮助)$/, // 总菜单.js
    ]

    // 其他需要豁免的系统命令
    this.systemWhitelist = [
      /^#(状态|统计)/, // status.js
      /^#设置主人/, // master.js
      /^#(全局)?添加/, // add.js
      /^#(全局)?删除/, // add.js
      /^#(全局)?(消息|词条)/, // add.js
      /^#?撤回$/, // recallReply.js
      /^#重启$/, // restart.js
      /^#关机$/, // restart.js
      /^#停(机|止)$/, // restart.js
      /^#开机$/, // restart.js
      /^#版本$/, // version.js
      /^#安装/, // install.js
      /^#更新/, // 更新相关命令
      /^#日志/, // 日志相关命令
      /^#全部更新/, // 全部更新命令
      /^#更新日志/, // 更新日志命令
    ]

    // 标记是否已经有插件处理了当前消息
    this.messageProcessed = new Set()

    // ws-plugin检测缓存
    this.wsPluginCache = {
      exists: null,
      apps: null,
      lastCheck: 0,
    }

    // 记录ws-plugin处理的消息
    this.wsProcessedMessages = new Set()

    // 记录等待处理的消息（用于检测是否有响应）
    this.pendingMessages = new Map()

    // 记录是否收到过gsuid_core的msg_id消息
    this.hasGsuidCoreResponse = false
    this.lastGsuidCoreTime = 0

    // 监听gsuid_core的msg_id消息
    this.setupMsgIdListener()
  }

  /**
   * accept方法，用于控制插件是否接受处理消息
   */
  async accept(e) {
    // 只允许QQBot使用
    if (!["QQBot"].includes(e.adapter_name)) {
      return false
    }

    // 处理所有消息
    if (!e.msg || !e.msg.trim()) {
      return false
    }

    return true
  }

  /**
   * 设置msg_id消息监听器
   */
  setupMsgIdListener() {
    // 监听gsuid_core发送的带msg_id的消息
    Bot.on("message", e => {
      // 检查是否是gsuid_core发送的带msg_id的消息
      if ((e.msg_id || e.message_id) && (e.adapter_name === "QQBot" || e.user_id === e.self_id)) {
        const currentTime = Date.now()
        // 防止重复记录（1秒内的重复消息忽略）
        if (!this.hasGsuidCoreResponse || currentTime - this.lastGsuidCoreTime > 1000) {
          this.hasGsuidCoreResponse = true
          this.lastGsuidCoreTime = currentTime
        }
      }
    })
  }

  async fallbackResponse(e) {
    // 只允许QQBot使用
    if (!["QQBot"].includes(e.adapter_name)) {
      return false
    }

    // 生成消息唯一标识（包含消息内容）
    const messageId = `${e.self_id}_${e.user_id}_${e.group_id || "private"}_${e.msg}_${Date.now()}`

    // 如果消息已经被处理过，不触发兜底响应
    if (this.messageProcessed.has(messageId)) {
      return false
    }

    // 检查消息是否为空
    if (!e.msg || !e.msg.trim()) {
      return false // 空消息，不触发兜底响应
    }

    // 检查是否是白名单命令（plugins/example）
    for (const regex of this.exampleWhitelist) {
      if (regex.test(e.msg)) {
        this.messageProcessed.add(messageId) // 标记为已处理
        return false // 不触发兜底响应
      }
    }

    // 检查是否是系统命令
    for (const regex of this.systemWhitelist) {
      if (regex.test(e.msg)) {
        this.messageProcessed.add(messageId) // 标记为已处理
        return false // 不触发兜底响应
      }
    }

    // 延迟执行，给ws-plugin充足的处理时间
    setTimeout(async () => {
      try {
        // 再次检查是否已被处理
        if (this.messageProcessed.has(messageId)) {
          return
        }

        // 检查ws-plugin是否存在
        const wsExists = await this.checkWsPluginExists()
        if (wsExists) {
          // 检查gsuid_core是否在12秒内有响应
          if (this.hasGsuidCoreResponse && Date.now() - this.lastGsuidCoreTime < 12000) {
            // gsuid_core有响应，不触发兜底
            this.messageProcessed.add(messageId)
            return
          }
        }

        // 标记消息为已处理，避免重复触发
        this.messageProcessed.add(messageId)

        // 清理过期的消息记录（保留最近1000条）
        if (this.messageProcessed.size > 1000) {
          const entries = Array.from(this.messageProcessed)
          this.messageProcessed.clear()
          entries.slice(-500).forEach(id => this.messageProcessed.add(id))
        }

        // 触发兜底响应
        await this.sendFallbackMessage(e)
      } catch (error) {
        console.error("兜底响应处理出错:", error)
      }
    }, 10000) // 延迟10秒执行，给gsuid_core充足时间

    return false // 不阻止其他插件处理
  }

  /**
   * 不再需要检查机器人回复
   */
  async checkForBotReply() {
    // 不再需要，直接检查msg_id
    return false
  }

  /**
   * 检查消息是否可能被ws-plugin处理
   */
  async mightBeProcessedByWSPlugin() {
    try {
      // 检查ws-plugin是否存在
      const wsExists = await this.checkWsPluginExists()
      if (!wsExists) {
        return false
      }

      // 如果ws-plugin存在，它可能会处理任何消息（通过message.js转发到gsuid_core）
      return true
    } catch (error) {
      console.error("检查ws-plugin处理可能性时出错:", error)
      return false
    }
  }

  /**
   * 检查ws-plugin是否存在
   */
  async checkWsPluginExists() {
    const now = Date.now()
    // 缓存5分钟
    if (this.wsPluginCache.lastCheck && now - this.wsPluginCache.lastCheck < 300000) {
      return this.wsPluginCache.exists
    }

    try {
      const wsPluginApps = await import("../ws-plugin/index.js").catch(() => null)
      this.wsPluginCache.exists = !!wsPluginApps
      this.wsPluginCache.apps = wsPluginApps?.apps || null
      this.wsPluginCache.lastCheck = now
      return this.wsPluginCache.exists
    } catch (error) {
      this.wsPluginCache.exists = false
      this.wsPluginCache.apps = null
      this.wsPluginCache.lastCheck = now
      return false
    }
  }

  /**
   * 检查ws-plugin是否已经处理完成了该消息
   */
  async checkWsPluginProcessed(e) {
    try {
      // 首先检查ws-plugin是否存在
      const wsExists = await this.checkWsPluginExists()
      if (!wsExists) {
        return false // ws-plugin不存在，认为未处理
      }

      // 生成消息标识
      const msgKey = `${e.self_id}_${e.user_id}_${e.group_id || "private"}_${e.msg}`

      // 如果已经记录为ws-plugin处理过的消息，直接返回true
      if (this.wsProcessedMessages.has(msgKey)) {
        return true
      }

      // 检查是否在等待列表中，如果在说明可能被ws-plugin处理
      if (this.pendingMessages.has(msgKey)) {
        // 检查是否超时（超过10秒认为未处理）
        const pendingData = this.pendingMessages.get(msgKey)
        if (Date.now() - pendingData.timestamp > 10000) {
          this.pendingMessages.delete(msgKey)
          return false
        }
        return true // 仍在等待处理
      }

      return false // 没有在处理中，ws-plugin不会处理
    } catch (error) {
      console.error("检查ws-plugin处理状态时出错:", error)
      return false
    }
  }

  /**
   * 发送兜底响应消息
   */
  async sendFallbackMessage(e) {
    try {
      // 简化的提示信息
      const textContent = `\r#  未识别的命令\r\r可能为命令错误，请查看帮助\r\r点击下方按钮快速访问功能菜单`

      const params = [
        {
          key: "text",
          values: [textContent],
        },
      ]

      // 创建markdown segment
      let md = segment.markdown({
        custom_template_id: "102072241_1752245006", // 使用与总菜单相同的模板ID
        params: params,
      })

      // 创建按钮数据
      const buttonData = {
        type: "keyboard",
        id: "102072241_1751292952", // 使用与总菜单相同的按钮ID
      }

      // 组合markdown和按钮
      let msg = [md, segment.raw(buttonData)]

      setTimeout(async () => {
        try {
          await this.reply(msg, false, { at: false })
        } catch (error) {
          console.error("发送兜底响应消息出错：", error)
          // 如果markdown发送失败，回退到普通文本
          await e.reply(`🤖 未识别的命令\n\n可能为命令错误，请查看帮助`)
        }
      }, 1000)
    } catch (error) {
      console.error("发送兜底响应消息出错：", error)
      // 最终回退到简单文本回复
      await e.reply(`🤖 未识别的命令\n\n可能为命令错误，请查看帮助`)
    }
  }

  /**
   * 检查文本中是否包含兑换码（参考全局转换markdown.js的逻辑）
   */
  hasRedemptionCode(text) {
    // 常见的兑换码模式
    const redemptionCodePatterns = [
      // 原神兑换码格式
      /[A-Z0-9]{12}/g, // 12位大写字母和数字组合
      /[A-Z0-9]{16}/g, // 16位大写字母和数字组合

      // 崩坏3兑换码格式
      /[A-Z0-9]{8,16}/g, // 8-16位大写字母和数字组合

      // 鸣潮兑换码格式
      /[A-Z0-9]{10,15}/g, // 10-15位大写字母和数字组合

      // 星穹铁道兑换码格式
      /[A-Z0-9]{12,16}/g, // 12-16位大写字母和数字组合

      // 通用兑换码关键词
      /兑换码/gi,
      /礼品码/gi,
      /激活码/gi,
      /cdkey/gi,
      /redemption/gi,
      /redeem/gi,

      // 包含特定游戏兑换码前缀的模式
      /YS[A-Z0-9]+/g, // 原神
      /BH3[A-Z0-9]+/g, // 崩坏3
      /MC[A-Z0-9]+/g, // 鸣潮
      /HSR[A-Z0-9]+/g, // 星穹铁道
    ]

    return redemptionCodePatterns.some(pattern => pattern.test(text))
  }
}
