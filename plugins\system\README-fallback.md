# 兜底响应机制插件

## 概述
这是一个为 TRSS-Yunzai 设计的兜底响应机制插件，当用户输入的命令没有被任何插件处理时，会提供友好的提示信息和建议。

## 功能特点

### 🎯 智能识别
- 自动识别未被处理的命令
- 支持以 `#` 开头的命令和特定的无前缀命令（如ww系列）
- 智能过滤已知的系统命令和插件命令

### 🛡️ 白名单保护
- 完整的 plugins/example 插件命令白名单
- 系统核心命令白名单
- ws-plugin 命令自动检测和豁免

### 🤖 友好提示
- 使用 Markdown + 按钮的形式展示
- 根据用户输入提供智能建议
- 多级回退机制确保消息能够发送

### ⚡ 高性能
- 优先级设置为无限，确保最后执行
- 消息去重机制避免重复触发
- 自动内存清理防止内存泄漏

## 使用场景

### 适用情况
✅ 用户输入了不存在的命令  
✅ 命令拼写错误  
✅ 新用户不熟悉可用命令  
✅ 需要引导用户使用正确的命令  

### 不适用情况
❌ 已有插件处理的命令
❌ 系统核心命令
❌ 非命令消息（不以#开头且不是已知命令格式）
❌ 非QQBot适配器的消息

## 安装方法

1. 将 `fallback.js` 文件放置在 `plugins/system/` 目录下
2. 重启 Yunzai 或使用热重载
3. 插件自动生效

## 配置说明

插件无需额外配置，开箱即用。所有的白名单和规则都已内置。

### 白名单命令包括：
- **plugins/example 插件**：
  - 带#前缀：#伤害总排行列表、#鸣潮图鉴列表、#天赋列表等
  - 无#前缀：ww系列命令、随机表情包列表、菜单、帮助等
- **系统命令**：#帮助、#状态、#重启、#安装等
- **ws-plugin**：自动检测相关命令

## 示例效果

当用户输入 `#不存在的命令` 时，会收到：

```
🤖 命令未识别

抱歉，我无法识别您输入的命令：#不存在的命令

🔍 建议：请检查命令拼写是否正确

💡 常用命令提示：
• 输入 #帮助 或 菜单 查看所有功能
• 输入 ww帮助 查看鸣潮相关功能
• 点击下方按钮快速访问功能菜单

[功能菜单按钮]
```

## 技术实现

- **优先级**：Infinity（最低优先级）
- **适配器限制**：仅QQBot
- **消息匹配**：正则表达式 `.*`
- **去重机制**：基于消息ID的Set集合
- **回退机制**：Markdown → 普通文本 → 简单文本

## 维护说明

如需添加新的白名单命令，请修改以下数组：
- `this.exampleWhitelist` - example插件命令
- `this.systemWhitelist` - 系统命令
- `this.wsPluginPatterns` - ws-plugin命令模式

## 兼容性

- ✅ TRSS-Yunzai v3.x
- ✅ QQBot 适配器
- ✅ 与其他插件完全兼容
- ✅ 支持热重载

## 许可证

本插件遵循与 TRSS-Yunzai 相同的开源许可证。
