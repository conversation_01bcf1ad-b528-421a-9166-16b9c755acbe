# 兜底响应机制插件使用示例

## 测试场景

### 1. 未知命令测试
**输入：** `#测试命令`
**预期结果：** 触发兜底响应，显示命令未识别提示

### 2. 拼写错误测试
**输入：** `#帮组` (帮助的错别字)
**预期结果：** 触发兜底响应，建议使用正确的帮助命令

### 3. 白名单命令测试
**输入：** `#帮助`
**预期结果：** 不触发兜底响应，由系统帮助插件处理

**输入：** `ww登录`
**预期结果：** 不触发兜底响应，由example插件处理

### 4. 智能建议测试

#### 帮助相关
**输入：** `#帮助123`
**预期建议：** "您可能想要：输入 #帮助 或 菜单 查看功能列表"

#### 鸣潮相关
**输入：** `#鸣潮测试`
**预期建议：** "您可能想要：输入 ww帮助 查看鸣潮相关功能"

#### 伤害相关
**输入：** `#伤害查询`
**预期建议：** "您可能想要：输入 #伤害总排行列表 查看伤害排行功能"

#### 评分相关
**输入：** `#评分查看`
**预期建议：** "您可能想要：输入 #评分总排行列表 查看评分排行功能"

#### 图鉴相关
**输入：** `#图鉴查看`
**预期建议：** "您可能想要：输入 #鸣潮图鉴列表 查看图鉴功能"

#### 天赋相关
**输入：** `#天赋查看`
**预期建议：** "您可能想要：输入 #鸣潮天赋列表 查看天赋功能"

#### 共鸣相关
**输入：** `#共鸣查看`
**预期建议：** "您可能想要：输入 #鸣潮共鸣链列表 查看共鸣链功能"

### 5. 非命令消息测试
**输入：** `你好` (不以#开头)
**预期结果：** 不触发兜底响应

### 6. 系统命令测试
**输入：** `#状态`
**预期结果：** 不触发兜底响应，由系统状态插件处理

**输入：** `#重启`
**预期结果：** 不触发兜底响应，由系统重启插件处理

### 7. ws-plugin命令测试
**输入：** `#ws状态`
**预期结果：** 不触发兜底响应，由ws-plugin处理

## 完整的响应示例

### 示例1：未知命令
```
用户输入：#未知功能

机器人回复：
🤖 命令未识别

抱歉，我无法识别您输入的命令：#未知功能

🔍 建议：请检查命令拼写是否正确

💡 常用命令提示：
• 输入 #帮助 或 菜单 查看所有功能
• 输入 ww帮助 查看鸣潮相关功能
• 点击下方按钮快速访问功能菜单

[功能菜单按钮]
```

### 示例2：包含关键词的命令
```
用户输入：#鸣潮角色

机器人回复：
🤖 命令未识别

抱歉，我无法识别您输入的命令：#鸣潮角色

🔍 您可能想要：输入 ww帮助 查看鸣潮相关功能

💡 常用命令提示：
• 输入 #帮助 或 菜单 查看所有功能
• 输入 ww帮助 查看鸣潮相关功能
• 点击下方按钮快速访问功能菜单

[功能菜单按钮]
```

## 测试步骤

1. **安装插件**
   - 将 fallback.js 放入 plugins/system/ 目录
   - 重启 Yunzai

2. **基础功能测试**
   - 输入已知命令，确认不触发兜底响应
   - 输入未知命令，确认触发兜底响应

3. **白名单测试**
   - 逐一测试白名单中的命令
   - 确认都不会触发兜底响应

4. **智能建议测试**
   - 输入包含关键词的未知命令
   - 检查是否给出正确的建议

5. **边界情况测试**
   - 测试非命令消息
   - 测试空消息
   - 测试特殊字符

## 故障排除

### 问题1：兜底响应没有触发
**可能原因：**
- 命令被其他插件处理了
- 不是以#开头的命令
- 不是QQBot适配器

**解决方法：**
- 检查是否有其他插件处理了该命令
- 确认输入的是以#开头的命令
- 确认使用的是QQBot适配器

### 问题2：白名单命令触发了兜底响应
**可能原因：**
- 白名单正则表达式不匹配
- 对应的插件未加载

**解决方法：**
- 检查白名单正则表达式
- 确认对应插件已正确加载

### 问题3：Markdown消息发送失败
**可能原因：**
- 模板ID不正确
- 适配器不支持Markdown

**解决方法：**
- 插件会自动回退到普通文本
- 检查控制台错误日志
